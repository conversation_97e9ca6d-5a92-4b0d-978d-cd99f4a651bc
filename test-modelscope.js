// 简单的测试脚本来验证ModelScope供应商实现
const { buildApiHandler } = require('./src/api/index.ts');

// 测试配置
const testConfig = {
    apiProvider: 'modelscope',
    modelscopeApiKey: '4093339f-b0fc-4812-a86b-11eb30d5a832',
    modelscopeModelId: 'qwen/qwen2.5-72b-instruct'
};

console.log('测试ModelScope供应商配置...');
console.log('配置:', testConfig);

try {
    // 创建API处理器
    const handler = buildApiHandler(testConfig);
    console.log('✅ ModelScope API处理器创建成功');
    
    // 获取模型信息
    const model = handler.getModel();
    console.log('✅ 模型信息获取成功:', model);
    
    console.log('\n🎉 ModelScope供应商实现完成！');
    console.log('\n供应商信息:');
    console.log('- 名称: ModelScope (魔搭)');
    console.log('- API URL: https://api-inference.modelscope.cn/v1/');
    console.log('- 测试API Key: 4093339f-b0fc-4812-a86b-11eb30d5a832');
    console.log('- API格式: 兼容OpenAI API范式');
    console.log('- 默认模型: qwen/qwen2.5-72b-instruct');
    
} catch (error) {
    console.error('❌ 测试失败:', error.message);
}
