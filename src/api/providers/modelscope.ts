import { type ModelScopeModelId, modelscopeDefaultModelId, modelscopeModels } from "@roo-code/types"

import type { ApiHandlerOptions } from "../../shared/api"

import { BaseOpenAiCompatibleProvider } from "./base-openai-compatible-provider"

export class ModelScopeHandler extends BaseOpenAiCompatibleProvider<ModelScopeModelId> {
	constructor(options: ApiHandlerOptions) {
		super({
			...options,
			providerName: "ModelScope",
			baseURL: "https://api-inference.modelscope.cn/v1",
			apiKey: options.modelscopeApiKey,
			defaultProviderModelId: modelscopeDefaultModelId,
			providerModels: modelscopeModels,
			defaultTemperature: 0.7,
		})
	}

	override getModel() {
		const id =
			this.options.modelscopeModelId && this.options.modelscopeModelId in modelscopeModels
				? (this.options.modelscopeModelId as ModelScopeModelId)
				: modelscopeDefaultModelId

		return { id, info: modelscopeModels[id] }
	}
}
