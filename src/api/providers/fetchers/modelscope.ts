import axios from "axios"

import type { ModelInfo } from "@roo-code/types"

import { parseApiPrice } from "../../../shared/cost"

export async function getModelScopeModels(apiKey?: string): Promise<Record<string, ModelInfo>> {
	const models: Record<string, ModelInfo> = {}

	try {
		const headers: Record<string, string> = {
			"Content-Type": "application/json",
		}

		if (apiKey) {
			headers["Authorization"] = `Bearer ${apiKey}`
		}

		const url = "https://api-inference.modelscope.cn/v1/models"
		const response = await axios.get(url, { headers })
		const rawModels = response.data.data

		if (Array.isArray(rawModels)) {
			for (const rawModel of rawModels) {
				const modelInfo: ModelInfo = {
					maxTokens: rawModel.max_tokens || 8192,
					contextWindow: rawModel.context_length || 32768,
					supportsImages: rawModel.multimodal?.includes("image") || false,
					supportsComputerUse: false,
					supportsPromptCache: false,
					inputPrice: parseApiPrice(rawModel.pricing?.input),
					outputPrice: parseApiPrice(rawModel.pricing?.output),
					description: rawModel.description || `ModelScope model: ${rawModel.id}`,
				}

				models[rawModel.id] = modelInfo
			}
		}
	} catch (error) {
		console.error(`Error fetching ModelScope models: ${JSON.stringify(error, Object.getOwnPropertyNames(error), 2)}`)
		
		// 如果API调用失败，返回默认的模型列表
		return {
			"qwen/qwen2.5-72b-instruct": {
				maxTokens: 8192,
				contextWindow: 32768,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.5,
				outputPrice: 2.0,
				description: "Qwen2.5-72B-Instruct是阿里云通义千问团队开发的72B参数大语言模型，具有强大的推理和代码生成能力",
			},
			"qwen/qwen2.5-32b-instruct": {
				maxTokens: 8192,
				contextWindow: 32768,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.3,
				outputPrice: 1.2,
				description: "Qwen2.5-32B-Instruct是阿里云通义千问团队开发的32B参数大语言模型",
			},
			"qwen/qwen2.5-14b-instruct": {
				maxTokens: 8192,
				contextWindow: 32768,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.2,
				outputPrice: 0.8,
				description: "Qwen2.5-14B-Instruct是阿里云通义千问团队开发的14B参数大语言模型",
			},
			"qwen/qwen2.5-7b-instruct": {
				maxTokens: 8192,
				contextWindow: 32768,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.1,
				outputPrice: 0.4,
				description: "Qwen2.5-7B-Instruct是阿里云通义千问团队开发的7B参数大语言模型",
			},
			"qwen/qwen2.5-coder-32b-instruct": {
				maxTokens: 8192,
				contextWindow: 32768,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.3,
				outputPrice: 1.2,
				description: "Qwen2.5-Coder-32B-Instruct是专门针对代码生成和编程任务优化的32B参数模型",
			},
			"qwen/qwen2.5-coder-14b-instruct": {
				maxTokens: 8192,
				contextWindow: 32768,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.2,
				outputPrice: 0.8,
				description: "Qwen2.5-Coder-14B-Instruct是专门针于代码生成和编程任务优化的14B参数模型",
			},
			"qwen/qwen2.5-coder-7b-instruct": {
				maxTokens: 8192,
				contextWindow: 32768,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.1,
				outputPrice: 0.4,
				description: "Qwen2.5-Coder-7B-Instruct是专门针对代码生成和编程任务优化的7B参数模型",
			},
			"deepseek-ai/deepseek-v3": {
				maxTokens: 8192,
				contextWindow: 64000,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.14,
				outputPrice: 0.28,
				description: "DeepSeek-V3是DeepSeek开发的最新一代大语言模型，具有强大的推理和代码能力",
			},
			"deepseek-ai/deepseek-coder-v2-instruct": {
				maxTokens: 8192,
				contextWindow: 163840,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.14,
				outputPrice: 0.28,
				description: "DeepSeek-Coder-V2-Instruct是专门为代码生成和编程任务设计的大语言模型",
			},
			"baichuan-inc/baichuan2-13b-chat": {
				maxTokens: 4096,
				contextWindow: 4096,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.1,
				outputPrice: 0.2,
				description: "百川2-13B-Chat是百川智能开发的13B参数对话模型",
			},
			"01-ai/yi-1.5-34b-chat": {
				maxTokens: 4096,
				contextWindow: 200000,
				supportsImages: false,
				supportsComputerUse: false,
				supportsPromptCache: false,
				inputPrice: 0.12,
				outputPrice: 0.12,
				description: "Yi-1.5-34B-Chat是零一万物开发的34B参数对话模型，支持200K上下文长度",
			},
		}
	}

	return models
}
